# DeepSeek API 配置文件
# 使用 DeepSeek API 替代 OpenAI API

######################################
# DeepSeek 模型配置                   #
######################################
model_config: &deepseek_client
  provider: OpenAIChatCompletionClient  # 使用 OpenAI 兼容的客户端
  config:
    model: deepseek-chat                # DeepSeek-V3 模型
    api_key: ***********************************  # 您的 DeepSeek API Key
    base_url: https://api.deepseek.com/v1  # DeepSeek API 基础URL
    max_retries: 10
    # 模型能力配置
    model_info:
      vision: true                      # DeepSeek-V3 支持视觉
      function_calling: true            # 支持函数调用
      json_output: true                 # 支持JSON输出
      family: deepseek                  # 模型家族
      structured_output: true           # 支持结构化输出
      multiple_system_messages: true   # 支持多个系统消息

# 推理模型配置（用于需要更强推理能力的任务）
deepseek_reasoner_config: &deepseek_reasoner_client
  provider: OpenAIChatCompletionClient
  config:
    model: deepseek-reasoner            # DeepSeek-R1 推理模型
    api_key: ***********************************
    base_url: https://api.deepseek.com/v1
    max_retries: 10
    model_info:
      vision: false                     # R1 模型主要用于推理，不支持视觉
      function_calling: true
      json_output: true
      family: deepseek
      structured_output: true
      multiple_system_messages: true

##########################
# 各个智能体的客户端配置    #
##########################
# 编排器 - 使用推理模型，需要更强的规划能力
orchestrator_client: *deepseek_reasoner_client

# 网页浏览器 - 使用标准模型，需要视觉能力
web_surfer_client: *deepseek_client

# 代码执行器 - 使用推理模型，需要更强的代码理解能力
coder_client: *deepseek_reasoner_client

# 文件浏览器 - 使用标准模型
file_surfer_client: *deepseek_client

# 动作守卫 - 使用标准模型，用于安全检查
action_guard_client: *deepseek_client

##########################
# 系统配置                #
##########################
# 协作规划模式
cooperative_planning: true

# 自主执行模式（建议先设为false进行测试）
autonomous_execution: false

# 每步最大动作数
max_actions_per_step: 5

# 最大轮次
max_turns: 20

# 审批策略
approval_policy: "auto-conservative"

# 允许重新规划
allow_for_replans: true

# 启用必应搜索（可选）
do_bing_search: false

# 模型上下文令牌限制
model_context_token_limit: 128000

# 允许的网站列表（可选，为空表示允许所有）
# allowed_websites:
#   - "https://example.com"
#   - "https://another-site.com"

##########################
# MCP 代理配置（可选）      #
##########################
# 如果需要扩展功能，可以添加 MCP 代理
# mcp_agent_configs:
#   - name: custom_agent
#     description: "自定义代理"
#     model_client: *deepseek_client
#     mcp_servers:
#       - server_name: example_server
#         server_params:
#           type: StdioServerParams
#           command: npx
#           args:
#             - -y
#             - "@example/mcp-server"
