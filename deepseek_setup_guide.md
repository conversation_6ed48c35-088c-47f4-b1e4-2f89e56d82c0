# DeepSeek API 配置指南

## 📋 配置文件说明

我为您创建了两个配置文件：

### 1. `deepseek_config.yaml` - 完整配置
- 使用 DeepSeek-V3 (deepseek-chat) 和 DeepSeek-R1 (deepseek-reasoner) 两个模型
- 针对不同智能体优化模型选择
- 包含详细的模型能力配置
- 适合生产环境使用

### 2. `deepseek_simple_config.yaml` - 简化配置
- 所有智能体都使用 deepseek-chat 模型
- 配置简单，适合初次测试
- 安全设置：所有操作都需要用户确认

## 🚀 使用方法

### 方法1：启动时指定配置文件
```bash
# 使用完整配置
magentic-ui --config deepseek_config.yaml --port 8081

# 使用简化配置（推荐初次测试）
magentic-ui --config deepseek_simple_config.yaml --port 8081
```

### 方法2：重命名为默认配置文件
```bash
# 将配置文件重命名为 config.yaml
cp deepseek_simple_config.yaml config.yaml

# 直接启动（会自动使用 config.yaml）
magentic-ui --port 8081
```

### 方法3：在UI中导入配置
1. 启动 Magentic-UI：`magentic-ui --port 8081`
2. 访问 http://localhost:8081
3. 点击右上角的设置图标
4. 在设置页面导入配置文件

## 🔧 配置详解

### API 配置
- **base_url**: `https://api.deepseek.com/v1` (DeepSeek API 地址)
- **api_key**: 您的 DeepSeek API 密钥
- **model**: `deepseek-chat` (DeepSeek-V3) 或 `deepseek-reasoner` (DeepSeek-R1)

### 模型选择建议
- **deepseek-chat**: 通用任务，支持视觉，速度快，成本低
- **deepseek-reasoner**: 复杂推理任务，逻辑能力强，适合编排和代码生成

### 安全设置
- **approval_policy**: 
  - `"always"`: 所有操作都需要确认（最安全）
  - `"auto-conservative"`: 敏感操作需要确认
  - `"never"`: 完全自动执行（不推荐）

## 🧪 测试建议

### 第一步：基础测试
```bash
# 使用简化配置启动
magentic-ui --config deepseek_simple_config.yaml --port 8081
```

测试任务：
- "帮我搜索今天的天气"
- "创建一个简单的Python脚本来计算斐波那契数列"

### 第二步：网页自动化测试
测试任务：
- "访问百度首页并搜索'人工智能'"
- "帮我查看GitHub上的热门项目"

### 第三步：文件处理测试
测试任务：
- "分析当前目录下的文件"
- "创建一个包含项目信息的README文件"

## 💰 成本优化建议

### 1. 模型选择
- 简单任务使用 `deepseek-chat`
- 复杂推理使用 `deepseek-reasoner`
- 根据实际需要调整各智能体的模型配置

### 2. 参数调整
```yaml
max_actions_per_step: 3      # 减少每步操作数
max_turns: 15               # 限制最大轮次
model_context_token_limit: 100000  # 控制上下文长度
```

### 3. 审批策略
- 测试阶段使用 `"always"` 避免不必要的API调用
- 熟悉后可改为 `"auto-conservative"`

## ⚠️ 注意事项

1. **API密钥安全**：不要将包含API密钥的配置文件提交到版本控制系统
2. **成本控制**：DeepSeek API相对便宜，但仍需注意使用量
3. **网络访问**：确保能够访问 `api.deepseek.com`
4. **Docker要求**：某些功能需要Docker支持

## 🔍 故障排除

### 常见问题

1. **API密钥错误**
   ```
   错误：401 Unauthorized
   解决：检查API密钥是否正确
   ```

2. **网络连接问题**
   ```
   错误：Connection timeout
   解决：检查网络连接和防火墙设置
   ```

3. **模型不存在**
   ```
   错误：Model not found
   解决：确认使用正确的模型名称（deepseek-chat 或 deepseek-reasoner）
   ```

## 📞 获取帮助

如果遇到问题，可以：
1. 检查 Magentic-UI 的日志输出
2. 参考官方文档
3. 在GitHub上提交issue

## 🎯 下一步

配置成功后，您可以：
1. 探索更多自动化场景
2. 集成到您的工业流程中
3. 开发自定义的MCP代理
4. 优化配置以适应特定需求
