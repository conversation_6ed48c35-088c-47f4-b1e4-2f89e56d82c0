#!/bin/bash

# Magentic-UI with DeepSeek API 启动脚本

echo ""
echo "========================================"
echo "   Magentic-UI with DeepSeek API"
echo "========================================"
echo ""

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装或未添加到PATH"
    echo "请先安装 Python 3.10 或更高版本"
    exit 1
fi

# 检查是否安装了magentic-ui
if ! python3 -c "import magentic_ui" &> /dev/null; then
    echo "❌ Magentic-UI 未安装"
    echo ""
    echo "正在安装 Magentic-UI..."
    pip3 install magentic-ui
    if [ $? -ne 0 ]; then
        echo "❌ 安装失败，请手动安装：pip3 install magentic-ui"
        exit 1
    fi
fi

# 检查配置文件
if [ ! -f "deepseek_simple_config.yaml" ]; then
    echo "❌ 配置文件 deepseek_simple_config.yaml 不存在"
    echo "请确保配置文件在当前目录下"
    exit 1
fi

echo "✅ 环境检查完成"
echo ""
echo "🚀 启动 Magentic-UI..."
echo "📄 使用配置文件: deepseek_simple_config.yaml"
echo "🌐 访问地址: http://localhost:8081"
echo ""
echo "💡 提示: 按 Ctrl+C 停止服务"
echo ""

# 启动服务
magentic-ui --config deepseek_simple_config.yaml --port 8081
