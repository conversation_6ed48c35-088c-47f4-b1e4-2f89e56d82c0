# 简化的 DeepSeek 配置文件
# 适合初次测试使用

######################################
# DeepSeek 基础配置                   #
######################################
model_config: &deepseek_client
  provider: OpenAIChatCompletionClient
  config:
    model: deepseek-chat
    api_key: ***********************************
    base_url: https://api.deepseek.com/v1
    max_retries: 5

##########################
# 所有智能体使用相同配置    #
##########################
orchestrator_client: *deepseek_client
web_surfer_client: *deepseek_client
coder_client: *deepseek_client
file_surfer_client: *deepseek_client
action_guard_client: *deepseek_client

##########################
# 基础系统设置             #
##########################
cooperative_planning: true
autonomous_execution: false
max_actions_per_step: 3
max_turns: 15
approval_policy: "always"  # 所有操作都需要确认，更安全
allow_for_replans: true
model_context_token_limit: 100000
