# DeepSeek API Configuration for Magentic-UI
# Clean configuration without special characters

model_config: &deepseek_client
  provider: OpenAIChatCompletionClient
  config:
    model: deepseek-chat
    api_key: ***********************************
    base_url: https://api.deepseek.com/v1
    max_retries: 5

# Agent configurations
orchestrator_client: *deepseek_client
web_surfer_client: *deepseek_client
coder_client: *deepseek_client
file_surfer_client: *deepseek_client
action_guard_client: *deepseek_client

# System settings
cooperative_planning: true
autonomous_execution: false
max_actions_per_step: 3
max_turns: 15
approval_policy: "always"
allow_for_replans: true
model_context_token_limit: 100000
