@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    Magentic-UI with DeepSeek API
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装或未添加到PATH
    echo 请先安装 Python 3.10 或更高版本
    pause
    exit /b 1
)

REM 检查是否安装了magentic-ui
python -c "import magentic_ui" >nul 2>&1
if errorlevel 1 (
    echo ❌ Magentic-UI 未安装
    echo.
    echo 正在安装 Magentic-UI...
    pip install magentic-ui
    if errorlevel 1 (
        echo ❌ 安装失败，请手动安装：pip install magentic-ui
        pause
        exit /b 1
    )
)

REM 检查配置文件
if not exist "deepseek_simple_config.yaml" (
    echo ❌ 配置文件 deepseek_simple_config.yaml 不存在
    echo 请确保配置文件在当前目录下
    pause
    exit /b 1
)

echo ✅ 环境检查完成
echo.
echo 🚀 启动 Magentic-UI...
echo 📄 使用配置文件: deepseek_simple_config.yaml
echo 🌐 访问地址: http://localhost:8081
echo.
echo 💡 提示: 按 Ctrl+C 停止服务
echo.

REM 启动服务
magentic-ui --config deepseek_simple_config.yaml --port 8081

pause
