#!/usr/bin/env python3
"""
DeepSeek API 连接测试脚本
用于验证 DeepSeek API 配置是否正确
"""

import asyncio
import json
import aiohttp
import yaml
from typing import Dict, Any


async def test_deepseek_api(api_key: str, base_url: str = "https://api.deepseek.com") -> bool:
    """测试 DeepSeek API 连接"""
    
    url = f"{base_url}/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 测试数据
    test_data = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "system", "content": "你是一个有用的AI助手。"},
            {"role": "user", "content": "请简单介绍一下你自己，用中文回答。"}
        ],
        "max_tokens": 100,
        "temperature": 0.7,
        "stream": False
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            print("🔄 正在测试 DeepSeek API 连接...")
            print(f"📡 API地址: {url}")
            print(f"🔑 API密钥: {api_key[:8]}...{api_key[-8:]}")
            
            async with session.post(url, headers=headers, json=test_data) as response:
                print(f"📊 响应状态码: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    print("✅ API连接成功！")
                    print(f"🤖 模型响应: {result['choices'][0]['message']['content']}")
                    print(f"📈 使用令牌: {result.get('usage', {})}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ API连接失败: {response.status}")
                    print(f"📝 错误信息: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ 连接异常: {str(e)}")
        return False


async def test_both_models(api_key: str, base_url: str = "https://api.deepseek.com") -> Dict[str, bool]:
    """测试两个模型"""
    
    results = {}
    models = ["deepseek-chat", "deepseek-reasoner"]
    
    for model in models:
        print(f"\n🧪 测试模型: {model}")
        print("-" * 50)
        
        url = f"{base_url}/chat/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        test_data = {
            "model": model,
            "messages": [
                {"role": "user", "content": "1+1等于多少？请简短回答。"}
            ],
            "max_tokens": 50,
            "temperature": 0.1,
            "stream": False
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=test_data) as response:
                    if response.status == 200:
                        result = await response.json()
                        print(f"✅ {model} 测试成功")
                        print(f"🤖 回答: {result['choices'][0]['message']['content']}")
                        results[model] = True
                    else:
                        error_text = await response.text()
                        print(f"❌ {model} 测试失败: {response.status}")
                        print(f"📝 错误: {error_text}")
                        results[model] = False
        except Exception as e:
            print(f"❌ {model} 连接异常: {str(e)}")
            results[model] = False
    
    return results


def load_config_from_yaml(config_file: str) -> Dict[str, Any]:
    """从YAML配置文件加载配置"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 提取API配置
        model_config = config.get('model_config', {})
        if isinstance(model_config, dict):
            api_config = model_config.get('config', {})
            return {
                'api_key': api_config.get('api_key'),
                'base_url': api_config.get('base_url', 'https://api.deepseek.com'),
                'model': api_config.get('model', 'deepseek-chat')
            }
        return {}
    except Exception as e:
        print(f"❌ 读取配置文件失败: {str(e)}")
        return {}


async def main():
    """主函数"""
    print("🚀 DeepSeek API 配置测试工具")
    print("=" * 60)
    
    # 方法1: 直接使用API密钥测试
    api_key = "sk-804e4f8858eb44858788d6caf69db9f8"
    base_url = "https://api.deepseek.com"
    
    print("\n📋 测试配置:")
    print(f"🔑 API密钥: {api_key[:8]}...{api_key[-8:]}")
    print(f"🌐 基础URL: {base_url}")
    
    # 基础连接测试
    print("\n" + "="*60)
    print("🔍 第一步: 基础API连接测试")
    print("="*60)
    
    success = await test_deepseek_api(api_key, base_url)
    
    if success:
        print("\n" + "="*60)
        print("🔍 第二步: 测试所有可用模型")
        print("="*60)
        
        model_results = await test_both_models(api_key, base_url)
        
        print("\n📊 测试结果汇总:")
        print("-" * 30)
        for model, result in model_results.items():
            status = "✅ 可用" if result else "❌ 不可用"
            print(f"{model}: {status}")
    
    # 方法2: 测试配置文件
    print("\n" + "="*60)
    print("🔍 第三步: 测试配置文件")
    print("="*60)
    
    config_files = ["deepseek_config.yaml", "deepseek_simple_config.yaml", "config.yaml"]
    
    for config_file in config_files:
        try:
            print(f"\n📄 测试配置文件: {config_file}")
            config = load_config_from_yaml(config_file)
            
            if config and config.get('api_key'):
                print(f"✅ 配置文件格式正确")
                print(f"🔑 API密钥: {config['api_key'][:8]}...{config['api_key'][-8:]}")
                print(f"🌐 基础URL: {config.get('base_url', '未设置')}")
                print(f"🤖 模型: {config.get('model', '未设置')}")
            else:
                print(f"⚠️  配置文件不存在或格式不正确")
                
        except FileNotFoundError:
            print(f"⚠️  配置文件 {config_file} 不存在")
        except Exception as e:
            print(f"❌ 配置文件 {config_file} 读取失败: {str(e)}")
    
    print("\n" + "="*60)
    print("🎯 测试完成！")
    print("="*60)
    
    if success:
        print("✅ DeepSeek API 配置正确，可以开始使用 Magentic-UI")
        print("\n🚀 启动命令:")
        print("   magentic-ui --config deepseek_simple_config.yaml --port 8081")
        print("\n🌐 访问地址:")
        print("   http://localhost:8081")
    else:
        print("❌ DeepSeek API 配置有问题，请检查:")
        print("   1. API密钥是否正确")
        print("   2. 网络连接是否正常")
        print("   3. API配额是否充足")


if __name__ == "__main__":
    asyncio.run(main())
